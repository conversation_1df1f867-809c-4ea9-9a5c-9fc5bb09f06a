# Dependency Injection (DI) Package

A clean dependency injection abstraction for the BioMedict monorepo, built on top of GetIt.

[![nonstop_cli](https://img.shields.io/badge/started%20with-nonstop_cli-166C4E.svg?style=flat-square)](https://pub.dev/packages/nonstop_cli)
[![melos](https://img.shields.io/badge/maintained%20with-melos-f700ff.svg?style=flat-square)](https://github.com/invertase/melos)

## Features

- Clean abstraction over GetIt dependency injection
- Automatic disposal of registered instances
- Type-safe dependency registration and retrieval
- Support for custom dispose functions
- Comprehensive error handling
- Easy testing with isolated instances

## Installation 💻

**❗ In order to start using Di you must have the [Flutter SDK][flutter_install_link] installed on your machine.**

Add to your `pubspec.yaml`:

```yaml
dependencies:
  di:
    path: ../../packages/di
```

## Quick Start

```dart
import 'package:di/di.dart';

// Create and initialize DI
final di = GetItDependencyInjection();
await di.init();

// Register a service (note: positional parameter)
final apiService = ApiService('https://api.example.com');
di.register<ApiService>(
  apiService,
  dispose: (service) => service.dispose(), // Optional cleanup
);

// Retrieve the service
final service = di.get<ApiService>();

// Check if registered
if (di.has<ApiService>()) {
  print('ApiService is registered');
}

// Clean up
await di.dispose();
```

## API Reference

### Core Methods

#### `init()`
Initialize the dependency injection container.

```dart
await di.init();
```

#### `register<T>(instance, {dispose})`
Register an instance with optional dispose function.

```dart
// Register with dispose function
di.register<MyService>(
  MyService(),
  dispose: (instance) => instance.cleanup(),
);

// Register without dispose function
di.register<MyService>(MyService());
```

#### `get<T>()`
Retrieve a registered instance.

```dart
final service = di.get<MyService>();
```

#### `has<T>()`
Check if a type is registered.

```dart
if (di.has<MyService>()) {
  // Service is available
}
```

#### `unregister<T>(instance)`
Unregister a specific type and call its dispose function.

```dart
// Unregister by type (pass null for instance)
await di.unregister<MyService>(null);

// Unregister specific instance
await di.unregister<MyService>(myServiceInstance);
```

#### `reset()`
Dispose all instances and reinitialize.

```dart
await di.reset();
```

#### `dispose()`
Dispose all instances and clean up.

```dart
await di.dispose();
```

## Usage Examples

### Basic Registration and Retrieval

```dart
// Initialize
final di = GetItDependencyInjection();
await di.init();

// Register services
final apiService = ApiService('https://api.example.com');
di.register<ApiService>(apiService);

final userRepo = UserRepository(di.get<ApiService>());
di.register<UserRepository>(userRepo);

// Use services
final repo = di.get<UserRepository>();
repo.fetchUsers();
```

### With Dispose Functions

```dart
// Register with automatic cleanup
di.register<DatabaseConnection>(
  DatabaseConnection(),
  dispose: (db) async => await db.close(),
);

di.register<FileManager>(
  FileManager(),
  dispose: (fm) => fm.cleanup(),
);

// When dispose() is called, all dispose functions will be executed
await di.dispose();
```

### Testing

```dart
void main() {
  group('MyService Tests', () {
    late GetItDependencyInjection di;

    setUp(() async {
      // Use isolated GetIt instance for testing
      di = GetItDependencyInjection(getIt: GetIt.asNewInstance());
      await di.init();

      // Register test dependencies
      di.register<ApiService>(MockApiService());
    });

    tearDown(() async {
      await di.dispose();
    });

    test('should work with mocked dependencies', () {
      final service = di.get<ApiService>();
      // Test with mocked service
    });
  });
}
```

[flutter_install_link]: https://docs.flutter.dev/get-started/install