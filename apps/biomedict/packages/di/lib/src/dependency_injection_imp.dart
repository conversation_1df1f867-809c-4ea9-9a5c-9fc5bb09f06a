import 'dart:async';
import 'package:get_it/get_it.dart';

import 'dependency_injection.dart';

/// Implementation of DependencyInjection using GetIt
class GetItDependencyInjection implements DependencyInjection {
  final GetIt _getIt;
  final Map<Type, DisposeFunc> _disposeFunctions = {};
  final Map<Type, Object> _registeredInstances = {};
  bool _isInitialized = false;

  /// Creates a new instance with optional GetIt instance
  /// If no instance is provided, uses GetIt.instance
  GetItDependencyInjection({GetIt? getIt}) : _getIt = getIt ?? GetIt.instance;

  @override
  Future<void> init() async {
    if (_isInitialized) {
      return;
    }

    // GetIt doesn't require explicit initialization
    // but we can use this to set up any global configurations
    _isInitialized = true;
  }

  @override
  Future<void> dispose() async {
    if (!_isInitialized) {
      return;
    }

    // Call dispose functions for all registered instances
    for (final entry in _disposeFunctions.entries) {
      final type = entry.key;
      final disposeFunc = entry.value;
      final instance = _registeredInstances[type];

      if (instance != null) {
        try {
          await disposeFunc(instance);
        } catch (e) {
          // Log error but continue disposing other instances
          // In a real app, you might want to use a proper logger
          print('Error disposing instance of type $type: $e');
        }
      }
    }

    // Clear dispose functions and registered instances maps
    _disposeFunctions.clear();
    _registeredInstances.clear();

    // Reset GetIt instance
    await _getIt.reset();

    _isInitialized = false;
  }

  @override
  void register<T extends Object>(
    final T instance, {
    final DisposeFunc? dispose,
  }) {
    if (!_isInitialized) {
      throw StateError('DependencyInjection must be initialized before registering dependencies');
    }

    // Register the instance as a singleton
    _getIt.registerSingleton<T>(instance);

    // Store the instance and dispose function if provided
    _registeredInstances[T] = instance;
    if (dispose != null) {
      _disposeFunctions[T] = dispose;
    }
  }

  @override
  Future<void> unregister<T extends Object>(final T? instance) async {
    if (!_isInitialized) {
      return;
    }

    if (!_getIt.isRegistered<T>()) {
      return;
    }

    // Call dispose function if it exists
    final disposeFunc = _disposeFunctions[T];
    if (disposeFunc != null) {
      try {
        final registeredInstance = instance ?? _registeredInstances[T];
        if (registeredInstance != null) {
          await disposeFunc(registeredInstance);
        }
      } catch (e) {
        // Log error but continue with unregistration
        print('Error disposing instance of type $T: $e');
      }
      _disposeFunctions.remove(T);
    }

    // Remove from our tracking
    _registeredInstances.remove(T);

    // Unregister from GetIt
    await _getIt.unregister<T>(instance: instance);
  }

  @override
  T get<T extends Object>() {
    if (!_isInitialized) {
      throw StateError('DependencyInjection must be initialized before getting dependencies');
    }

    return _getIt.get<T>();
  }

  @override
  bool has<T extends Object>() {
    if (!_isInitialized) {
      return false;
    }

    return _getIt.isRegistered<T>();
  }

  @override
  Future<void> reset() async {
    await dispose();
    await init();
  }

  /// Check if the dependency injection is initialized
  bool get isInitialized => _isInitialized;

  /// Get the underlying GetIt instance (for advanced usage)
  GetIt get getIt => _getIt;
}
