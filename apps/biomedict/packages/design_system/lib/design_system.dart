import 'package:flutter/material.dart';

import 'generated/theme.dart';
import 'generated/util.dart';

export 'generated/theme.dart';
export 'generated/util.dart';

class DesignSystem {
  static MaterialTheme buildTheme(
    BuildContext context, {
    String bodyFont = "Poppins",
    String displayFont = "Poppins",
  }) {
    final textTheme = createTextTheme(context, bodyFont, displayFont);
    final theme = MaterialTheme(textTheme);
    return theme;
  }
}
